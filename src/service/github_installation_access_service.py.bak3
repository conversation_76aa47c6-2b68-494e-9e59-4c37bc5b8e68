from typing import Any, List, Optional

from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import (AccessType, GithubInstallation,
                                  GitHubInstallationAccess,
                                  GithubInstallationStatus, GitHubProjectRepo,
                                  TeamMember, VersionControlSystem)
from sqlalchemy.orm import Session

from src.api.models import SecretsOutput
from src.api.routes.secret_manager import get_github_secret_value, get_access_tokenfrom src.error.errors import ResourceNotFound

def get_github_installations_by_user(
    user_id: str,
    session: Optional[Session] = None
) -> Optional[List[GithubInstallation]]:
    """
    Fetches GitHub installations accessible by a user.

    This function retrieves GitHub installations associated with a user's teams
    and direct access using their user ID. It utilizes a database session
    to query the necessary data.

    :param user_id: The ID of the user for whom GitHub installations
                    are being fetched.
    :param session: Optional database session instance.
    :return: A list of GitHub installations accessible to the user,
             if any. Returns None if no data is found.
    """
    with get_db_session(session) as session:
        # Retrieve all team IDs.
        team_members = session.query(TeamMember.team_id).filter(
            TeamMember.user_id == user_id
        ).all()
        team_ids = [team_member[0] for team_member in team_members]
        team_ids.append(user_id)

        # Retrieve integration IDs.
        integration_info = session.query(
            GitHubInstallationAccess.integration_id
        ).filter(
            GitHubInstallationAccess.entity_id.in_(team_ids)
        ).all()
        integration_ids = {integration_info[0] for integration_info in integration_info}

        if not integration_ids:
            return []

        github_installations = session.query(GithubInstallation).filter(
            GithubInstallation.id.in_(integration_ids),
            GithubInstallation.status == GithubInstallationStatus.ACTIVE
        ).all()
        if github_installations and not session:
            session.expunge_all()
        return github_installations
    
def get_only_github_installations_by_user(
    user_id: str,
    session: Optional[Session] = None
) -> Optional[List[GithubInstallation]]:
    """
    Fetches GitHub installations accessible by a user.

    This function retrieves GitHub installations associated with a user's teams
    and direct access using their user ID. It utilizes a database session
    to query the necessary data.

    :param user_id: The ID of the user for whom GitHub installations
                    are being fetched.
    :param session: Optional database session instance.
    :return: A list of GitHub installations accessible to the user,
             if any. Returns None if no data is found.
    """
    with get_db_session(session) as session:
        # Retrieve all team IDs.
        team_members = session.query(TeamMember.team_id).filter(
            TeamMember.user_id == user_id
        ).all()
        team_ids = [team_member[0] for team_member in team_members]
        team_ids.append(user_id)

        # Retrieve integration IDs.
        integration_info = session.query(
            GitHubInstallationAccess.integration_id
        ).filter(
            GitHubInstallationAccess.entity_id.in_(team_ids)
        ).all()
        integration_ids = {integration_info[0] for integration_info in integration_info}

        if not integration_ids:
            return []

        github_installations = session.query(GithubInstallation).filter(
            GithubInstallation.id.in_(integration_ids),
            GithubInstallation.status == GithubInstallationStatus.ACTIVE,
            GithubInstallation.svc_type == VersionControlSystem.GITHUB
        ).all()
        if github_installations and not session:
            session.expunge_all()
        return github_installations



def delete_by_integration_id(integration_id: str, session: Optional[Session] = None) -> Any:
    """
    Deletes records from the GitHubInstallationAccess table filtered by the
    provided integration ID.

    :param integration_id: The integration ID used to filter records for deletion.
    :type integration_id: str
    :param session: Optional database session instance. Defaults to None.
    :type session: Optional[Session]
    :return: Count of deleted records.
    :rtype: Any
    """
    with get_db_session(session) as session:
        deleted_count = session.query(GitHubInstallationAccess).filter(
            GitHubInstallationAccess.integration_id == integration_id
        ).delete()

    return deleted_count


def search_github_installation_access_by_entity_ids(
        integration_id: str, entity_ids: List[str],
        session: Optional[Session] = None) -> Optional[GitHubInstallationAccess]:
    """
    Search for GitHub installation access linked to specific entity IDs.

    :param integration_id: ID of the integration.
    :param entity_ids: List of entity IDs to be searched.
    :param session: Database session to use, can be None.
    :return: GitHubInstallationAccess object if found, otherwise None.
    """
    with get_db_session(session) as session:
        github_installation_access = session.query(GitHubInstallationAccess).filter(
            GitHubInstallationAccess.integration_id == integration_id,
            GitHubInstallationAccess.entity_id.in_(entity_ids)
        ).first()

        if github_installation_access and not session:
            session.expunge_all(github_installation_access)
        return github_installation_access


def get_active_github_installation_by_github_project_repo_id(
        github_project_repo_id: str,
        session: Optional[Session] = None
) -> Optional[GithubInstallation]:
    """
    Get GitHub installation for a specific repository.

    :param repo_id: ID of the repository to find installation for.
    :param session: Database session to use, can be None.
    :return: GithubInstallation object if found, otherwise None.
    """
    with get_db_session(session) as db_session:
        # Find the project repo record
        # Choose first record, maps to same org_id
        github_project_repo = db_session.query(GitHubProjectRepo).filter(
            GitHubProjectRepo.id == github_project_repo_id
        ).first()

        if not github_project_repo:
            logger.debug(f"Unable to fetch github project repo from db with project_repo_id {github_project_repo_id}")
            return None

        # Find the active GitHub installation for this organization
        # Assume there is always one record, order by created_at for determinism
        github_installation = db_session.query(GithubInstallation).filter(
            GithubInstallation.target_id == github_project_repo.org_id,
            GithubInstallation.status == GithubInstallationStatus.ACTIVE
        ).order_by(GithubInstallation.created_at.desc()).first()

        if not github_installation:
            logger.debug(
                f"Unable to fetch github installation from db with project_repo_id {github_project_repo_id} \
                and org_id {github_project_repo.org_id}"
            )
            return None

        return github_installation


def get_github_project_repo_by_id(github_project_repo_id: str, session: Optional[Session] = None) -> Optional[GitHubProjectRepo]:
    """
    Get GitHub project repo by id.

    :param github_project_repo_id: ID of the project repo to find.
    :param session: Database session to use, can be None.
    :return: GitHubProjectRepo object if found, otherwise None.
    """
    with get_db_session(session) as session:
        github_project_repo = session.query(GitHubProjectRepo).filter(
            GitHubProjectRepo.id == github_project_repo_id
        ).first()

        return github_project_repo


def get_active_github_installation_by_repo_id(
        repo_id: str,
        session: Optional[Session] = None
) -> Optional[GithubInstallation]:
    """
    Get GitHub installation for a specific repository.

    :param repo_id: ID of the repository to find installation for.
    :param session: Database session to use, can be None.
    :return: GithubInstallation object if found, otherwise None.
    """
    with get_db_session(session) as db_session:
        # Find the project repo record
        # Choose first record, maps to same org_id
        github_project_repo = db_session.query(GitHubProjectRepo).filter(
            GitHubProjectRepo.repo_id == repo_id
        ).first()

        if not github_project_repo:
            logger.debug(f"Unable to fetch github project repo from db with repo_id {repo_id}")
            return None

        # Find the active GitHub installation for this organization
        # Assume there is always one record, order by created_at for determinism
        github_installation = db_session.query(GithubInstallation).filter(
            GithubInstallation.target_id == github_project_repo.org_id,
            GithubInstallation.status == GithubInstallationStatus.ACTIVE
        ).order_by(GithubInstallation.created_at.desc()).first()

        if not github_installation:
            logger.debug(
                "Unable to fetch github installation from db with repo_id %s and org_id %s",
                repo_id, github_project_repo.org_id
            )
            return None

        # Only expunge if we created our own session (session param was None)
        if github_installation and session is None:
            db_session.expunge_all()

        return github_installation


def get_active_azure_installation_by_user_id(
        user_id: str,
        session: Optional[Session] = None
) -> Optional[GithubInstallation]:
    """
    Get GitHub installation for a specific user.

    :param user_id: ID of the user to find installation for.
    :param session: Database session to use, can be None.
    :return: GithubInstallation object if found, otherwise None.
    """
    with get_db_session(session) as db_session:
        # Find the active GitHub installation for this user
        # Assume there is always one record, order by created_at for determinism
        github_installation = db_session.query(GithubInstallation).filter(
            GithubInstallation.user_id == user_id,
            GithubInstallation.status == GithubInstallationStatus.ACTIVE,
            GithubInstallation.svc_type == VersionControlSystem.AZURE_DEVOPS
        ).order_by(GithubInstallation.created_at.desc()).first()

        if not github_installation:
            logger.debug(f"Unable to fetch github installation from db with user_id {user_id}")
            return None

        # Only expunge if we created our own session (session param was None)
        if github_installation and session is None:
            db_session.expunge_all()

        return github_installation


def get_github_installation_by_user_and_svc(user_id: str, svc_type: str,
                                                    session: Optional[Session] = None) -> Optional[GithubInstallation]:
    """
    Retrieve GitHub installation by user and target name.

    :param user_id: The ID of the user.
    :type user_id: str
    :param target_name: The target name of the GitHub installation.
    :type target_name: str
    :param session: Optional database session.
    :type session: Optional[Session]
    :return: A list of GithubInstallation objects or None if no installations are found.
    :rtype: Optional[GithubInstallation]
    """
    with get_db_session(session) as session:
        # Retrieve all team IDs.
        team_members = session.query(TeamMember.team_id).filter(TeamMember.user_id == user_id).all()
        team_ids = [team_member[0] for team_member in team_members]
        team_ids.append(user_id)

        # Retrieve integration IDs.
        integration_info = session.query(GitHubInstallationAccess.integration_id).filter(
            GitHubInstallationAccess.entity_id.in_(team_ids)
        ).all()
        integration_ids = {integration_info[0] for integration_info in integration_info}

        if not integration_ids:
            return None

        github_installation = session.query(GithubInstallation).filter(
            GithubInstallation.id.in_(integration_ids),
            GithubInstallation.status == GithubInstallationStatus.ACTIVE,
            GithubInstallation.svc_type == svc_type
        ).first()
        if github_installation and not session:
            session.expunge_all()
        return github_installation


def save_installation_access(github_installation_access: GitHubInstallationAccess,
                             session: Optional[Session] = None) -> GitHubInstallationAccess:
    with get_db_session(session) as session:
        session.add(github_installation_access)
        session.flush()
        return github_installation_access


def create_github_installation_access_record_for_user(integration_id: str, user_id: str,
                                                      session: Optional[Session] = None) -> GitHubInstallationAccess:
    with get_db_session(session) as session:
        github_installation_access = GitHubInstallationAccess(
            integration_id=integration_id,
            entity_id=user_id,
            access_type=AccessType.USER,
            is_owner=True
        )
        session.add(github_installation_access)
        session.flush()
        return github_installation_access


# ------------------------------------------------------------------------------
# Github helpers and handler
# ------------------------------------------------------------------------------
def fetch_github_secret(installation_id: str) -> SecretsOutput:
    """

    This function retrieves the latest GitHub token and metadata associated with the given
    installation ID. The resulting token data is returned wrapped in a SecretsOutput object,
    including SCM type and optional setup metadata.

    :param installation_id: The GitHub App installation ID tied to the repository.
    :return: SecretsOutput object containing access token, code, installation ID, setup action, and SCM type.
    :raises ResourceNotFound: If the token is missing or the installation is not found.
    """

    secret = get_github_secret_value(installation_id, "latest")

    if not secret:
        logger.error(f"[GitHub][AccessToken] No token found for installation_id: {installation_id}")
        raise ResourceNotFound(f"No GitHub access token found for installation {installation_id}")

    output = SecretsOutput(
        accessToken=get_access_token(installation_id),
        code=secret.get("code"),
        installationID=installation_id,
        setupAction=secret.get("setup_action"),
        scvType=VersionControlSystem.GITHUB.value
    )

    logger.info(f"[GitHub][AccessToken] Successfully fetched access token for installation_id: {installation_id}")
    return output
