import hashlib
import hmac
from http.client import H<PERSON><PERSON><PERSON>x<PERSON>

from blitzy_utils.logger import logger
from cachetools import TTLCache, cached
from flask import Blueprint, jsonify, request
from flask_utils.decorators import flask_pydantic_response

from src.api.handlers.installation_event_handler import \
    handle_installation_event
from src.api.handlers.pr_event_handler import handle_pr_event
from src.api.handlers.repository_event_handler import handle_repository_event
from src.api.routes.operations import operations_bp
from src.api.routes.repositories import repositories_bp
from src.api.routes.secret_manager import secret_bp
from src.api.routes.users import users_bp
from src.consts import GITHUB_WEBHOOK_SECRET
from src.error.errors import ResourceNotFound
from src.service.azure_service import fetch_azure_secret
from src.service.git_installation_service import \
    get_active_installation_by_target_id
from src.service.github_installation_access_service import \
    get_github_project_repo_by_id

SIGNATURE_HEADER_KEY = "X-Hub-Signature-256"
github_bp = Blueprint("github_bp", __name__, url_prefix="/v1/github")

# Cache for Azure DevOps access tokens - 50 minutes TTL (tokens expire after 1 hour)
azure_token_cache = TTLCache(maxsize=1000, ttl=3000)


@cached(azure_token_cache)
def get_cached_azure_secret(installation_id: str):
    """
    Cached wrapper for fetch_azure_secret to provide additional in-memory caching.

    This adds an extra layer of caching on top of the existing Azure secret caching
    to avoid repeated calls to the secret storage system for the same installation_id.

    :param installation_id: The installation ID to fetch the secret for
    :return: SecretsOutput object containing the access token
    """
    logger.info(f"[Azure][Cache] Fetching Azure secret for installation_id: {installation_id}")
    result = fetch_azure_secret(installation_id)
    logger.info(f"[Azure][Cache] Successfully fetched Azure secret for installation_id: {installation_id}")
    return result

github_bp.register_blueprint(users_bp)
github_bp.register_blueprint(secret_bp)
github_bp.register_blueprint(repositories_bp)
github_bp.register_blueprint(operations_bp)


@github_bp.route("/webhook", methods=["POST"])
def webhook():
    # Print all headers
    signature = request.headers.get(SIGNATURE_HEADER_KEY)

    verify_signature(request.data, signature)
    logger.debug("Verified signature.")

    event_type = request.headers.get("X-GitHub-Event")
    event_handler_factory(event_type, payload=request.json)

    return jsonify({"status": "success"}), 200


def verify_signature(payload_body: bytes, signature_header: str):
    """
    Verify that the payload was sent from GitHub by validating SHA256. Raise and return 403 if not authorized.

    :param payload_body: The original request body to verify.
    :param signature_header: The signature header received from GitHub.
    """
    if not signature_header:
        raise HTTPException(status_code=403, detail=f"{SIGNATURE_HEADER_KEY} header is missing!")

    expected_signature = generate_expected_signature(payload_body, GITHUB_WEBHOOK_SECRET)

    if not hmac.compare_digest(expected_signature, signature_header):
        raise HTTPException(status_code=403, detail="Signature mismatch! The request could not be verified.")


def generate_expected_signature(payload_body: bytes, secret: str) -> str:
    """Generate the expected HMAC SHA256 signature."""
    generated_hash = hmac.new(secret.encode("utf-8"), msg=payload_body, digestmod=hashlib.sha256)
    return "sha256=" + generated_hash.hexdigest()


def event_handler_factory(event: str, payload: dict):
    """Factory function to create event handlers."""
    logger.info(f"Handling github event {event}")
    if event == "installation":
        handle_installation_event(payload)
    elif event == "repository":
        handle_repository_event(payload)
    elif event == "pull_request":
        handle_pr_event(payload)
    else:
        logger.warning(f"No handler for event {event}")
        logger.debug(f"Payload: {payload}")


@github_bp.route("/repositories/<github_project_repo_id>/access-token", methods=["GET"])
@flask_pydantic_response
def get_access_token_by_installation_id(github_project_repo_id: str):
    """Get complete GitHub installation information with organization details using map_to_model pattern."""
    github_project_repo = get_github_project_repo_by_id(github_project_repo_id)
    if not github_project_repo:
        raise ResourceNotFound(f"No Github project repo found for id: {github_project_repo_id}")

    org_id = github_project_repo.org_id

    # Check if org_id is null or empty
    if org_id is None or org_id.strip() == "":
        logger.error(f"org ID is null or empty for github_project_repo_id: {github_project_repo_id}")
        raise ResourceNotFound(f"organization ID is missing for repository: {github_project_repo_id}")

    # do not use installation_id from github_project_repo table.
    installation = get_active_installation_by_target_id(org_id)
    if not installation:
        logger.error(f"No active installation found for org_id: {org_id}")
        raise ResourceNotFound(f"No active installation found for org_id: {org_id}")
    installation_id = installation.installation_id

    # Check if installation_id is null or empty
    if installation_id is None or installation_id.strip() == "":
        logger.error(f"Installation ID is null or empty for github_project_repo_id: {github_project_repo_id}")
        raise ResourceNotFound(f"Installation ID is missing for repository: {github_project_repo_id}")

    logger.info(f"Fetching access token for installation_id: {installation_id}, org_id: {org_id}")

    secret = get_cached_azure_secret(installation_id)
    access_token = secret.accessToken

    try:
        response = {
            "access_token": access_token,
            "organization": github_project_repo.org_name,
            "installation_id": installation_id,
            "org_id": org_id,
            "repo_id": github_project_repo.repo_id,
            "repo_name": github_project_repo.repo_name,
            "azure_org_id": github_project_repo.azure_org_id,
            "azure_project_id": github_project_repo.azure_project_id,
            "project_id": github_project_repo.project_id,
            "id": github_project_repo.id
            }

        return response, 200
    except Exception as e:
        logger.error(f"Failed to resolve organization ID: {org_id}. Error: {str(e)}")
        raise
