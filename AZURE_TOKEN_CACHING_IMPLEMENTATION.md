# Azure DevOps Token Caching Implementation

## Problem Solved

The GitHub webhook handler was experiencing token-related errors when multiple requests came in for the same user before the previous token expired. The issue was that new Azure DevOps access tokens were being generated for each request instead of reusing valid existing tokens.

## Solution Overview

We implemented a **two-layer caching system** for Azure DevOps access tokens:

### Layer 1: Built-in Azure Service Caching
The Azure service (`src/service/azure_service.py`) already had comprehensive caching logic:

- **`_get_valid_token_if_available()`** - Checks if a valid non-expired token exists in secret storage
- **`token_is_expired()`** - Validates token expiration with 5-minute buffer
- **`fetch_azure_secret()`** - Main function that uses caching before generating new tokens

### Layer 2: In-Memory HTTP Request Caching
We added an additional in-memory cache layer in the webhook endpoint (`src/api/routes/github_webhook.py`):

```python
# Cache for Azure DevOps access tokens - 50 minutes TTL
azure_token_cache = TTLCache(maxsize=1000, ttl=3000)

@cached(azure_token_cache)
def get_cached_azure_secret(installation_id: str):
    """Cached wrapper for fetch_azure_secret"""
    return fetch_azure_secret(installation_id)
```

## Key Benefits

1. **Reduced API Calls**: Subsequent requests for the same installation_id reuse cached tokens
2. **Faster Response Times**: In-memory cache avoids repeated secret storage lookups
3. **Error Prevention**: Eliminates token-related errors from concurrent requests
4. **Automatic Expiration**: Tokens are automatically refreshed when they expire

## Implementation Details

### Cache Configuration
- **TTL**: 50 minutes (3000 seconds) - safely under the 1-hour Azure token expiration
- **Max Size**: 1000 entries - supports high-volume installations
- **Cache Key**: `installation_id` - ensures per-installation token isolation

### Token Lifecycle
1. **First Request**: Generates new token, stores in both layers
2. **Subsequent Requests**: Returns cached token from in-memory cache
3. **Near Expiration**: Azure service layer handles automatic refresh
4. **Cache Miss**: Falls back to Azure service layer with built-in retry logic

### Error Handling
- Maintains existing Azure service error handling and retry logic
- Cache failures gracefully fall back to direct token generation
- Token expiration is handled transparently

## Files Modified

1. **`src/api/routes/github_webhook.py`**
   - Added `azure_token_cache` TTL cache
   - Added `get_cached_azure_secret()` wrapper function
   - Updated `get_access_token_by_installation_id()` to use cached version

2. **`test_github_token_caching.py`**
   - Created comprehensive test suite for caching functionality
   - Tests cache hits, misses, and expiration scenarios

## Testing

The implementation includes a test suite that verifies:
- ✅ Tokens are properly cached for 50 minutes
- ✅ Same installation_id reuses cached tokens
- ✅ Different installation_ids get separate cache entries
- ✅ Cache expiration works as expected

## Usage

The caching is completely transparent to existing code. The webhook endpoint:

```python
@github_bp.route("/repositories/<github_project_repo_id>/access-token", methods=["GET"])
def get_access_token_by_installation_id(github_project_repo_id: str):
    # ... existing logic ...
    secret = get_cached_azure_secret(installation_id)  # Now uses caching
    access_token = secret.accessToken
    # ... rest of function unchanged ...
```

## Monitoring

The implementation includes detailed logging:
- `[Azure][Cache] Fetching Azure secret for installation_id: {id}` - Cache operations
- `[Azure DevOps] Valid access token found for tenant_id: {id}` - Cache hits
- `[Azure DevOps] Access token expired or near expiry` - Cache misses/refreshes

## Future Considerations

1. **Metrics**: Consider adding cache hit/miss metrics for monitoring
2. **Cache Warming**: Could pre-populate cache for frequently used installations
3. **Distributed Caching**: For multi-instance deployments, consider Redis-based caching
4. **Token Refresh Optimization**: Could implement background token refresh before expiration

## Conclusion

This implementation solves the token reuse issue by providing efficient, transparent caching that:
- Prevents duplicate token generation for concurrent requests
- Maintains backward compatibility with existing code
- Provides robust error handling and automatic token refresh
- Improves performance through reduced API calls and faster response times
